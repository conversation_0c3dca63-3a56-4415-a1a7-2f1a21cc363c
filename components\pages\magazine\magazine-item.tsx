import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

interface MagazineItemProps {
   year: number;
   coverImage: string;
   description: string;
   title: string;
}

export default function MagazineItem({
   year,
   coverImage,
   description,
   title,
}: MagazineItemProps) {
   return (
      <article className="group mb-16 overflow-hidden shadow-lg">
         {/* Magazine Cover Image */}
         <div className="relative aspect-[16/6] w-full overflow-hidden">
            <Image
               src={coverImage}
               alt={`Mlist Magazine ${year} cover`}
               fill
               className="object-cover transition-transform duration-300 group-hover:scale-105"
            />
            {/* Cover Overlay with Year */}
            <div className="absolute inset-0 bg-black/20"></div>
            <div className="absolute bottom-4 left-4 bg-black/70 px-4 py-2 text-white">
               <p className="text-sm font-medium">{year} Edition</p>
            </div>
         </div>

         {/* Content Section */}
         <div className="space-y-4 p-6">
            {/* Title and Year */}
            <div>
               <h2 className="text-lg font-semibold text-gray-900 md:text-xl">
                  {title}
               </h2>
            </div>

            {/* Description */}
            <div>
               <p className="text-sm leading-relaxed text-gray-700 md:text-base">
                  {description}
               </p>
            </div>

            {/* Download Button */}
            <div>
               <Button
                  className="rounded-xl px-6 py-5"
                  aria-label={`Download Mlist Magazine ${year}`}
               >
                  <Link
                     href="#"
                     className="flex gap-2 transition-all duration-300"
                  >
                     <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="mr-2"
                     >
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                        <polyline points="7,10 12,15 17,10" />
                        <line x1="12" x2="12" y1="15" y2="3" />
                     </svg>
                     Download PDF
                  </Link>
               </Button>
            </div>
         </div>
      </article>
   );
}

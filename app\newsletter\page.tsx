import NewsletterSignup from "@/components/pages/newsletter/newsletter-signup";
import type { Metadata } from "next";

export const metadata: Metadata = {
   title: "Newsletter | Mlist",
   description:
      "Stay updated with the latest from Mlist. Subscribe to our newsletter for exclusive content, featured creatives, and behind-the-scenes stories from the creative community.",
};

export default function NewsletterPage() {
   return (
      <main className="mt-20 min-h-screen md:mt-25">
         <div className="relative bg-gray-100 py-14">
            <div className="mx-auto max-w-[1380px] px-4 md:px-8">
               <h1 className="text-2xl font-bold md:text-3xl">Newsletter</h1>
               <p className="text-md mt-4 text-gray-600">
                  Join our community of creative professionals and stay informed
                  about the latest featured talent, industry insights, and
                  exclusive content from the Mlist community.
               </p>
            </div>
         </div>
         <NewsletterSignup />
      </main>
   );
}

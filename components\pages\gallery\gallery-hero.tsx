import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

export default function GalleryHero() {
   return (
      <section className="relative h-screen overflow-hidden">
         <div className="absolute inset-0 z-0">
            <Image
               src="/images/hero/list-bg.png"
               alt="Creative professional"
               fill
               priority
               className="object-cover grayscale"
            />
         </div>
         <div className="relative z-10 mx-auto flex h-full max-w-[1380px] items-center justify-start px-4 md:px-8">
            <div className="relative aspect-[16/6] w-[600px] -translate-y-10 md:w-[800px]">
               <Image
                  src="/images/hero/nominate-a-creative.png"
                  alt="Nominate a creative"
                  fill
                  priority
               />
            </div>
         </div>
         <div className="relative mx-auto w-full max-w-[1380px] px-4 md:px-8">
            <Button
               asChild
               className="absolute bottom-35 left-0 z-20 mx-8 rounded-2xl border-2 border-gray-400 bg-black px-8 py-5 text-lg text-gray-200 shadow-md shadow-black hover:bg-gray-950 md:mx-8"
               variant="secondary"
            >
               <Link
                  href="https://docs.google.com/forms/d/e/1FAIpQLSew1e_uhrpuLbtEVDM3gwjPzqiaDmW8KNQRgKy1BIJvCq97JA/viewform?usp=dialog"
                  target="_blank"
               >
                  Nominate
               </Link>
            </Button>
         </div>
      </section>
   );
}

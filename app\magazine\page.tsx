import MagazineArchive from "@/components/pages/magazine/magazine-archive";
import MagazineHero from "@/components/pages/magazine/magazine-hero";
import type { Metadata } from "next";

export const metadata: Metadata = {
   title: "Magazine | Mlist",
   description:
      "Explore our annual magazine collections featuring exceptional creatives from the Mlist community. Download and discover the stories behind the talent.",
};

export default function MagazinePage() {
   return (
      <main className="min-h-screen">
         <MagazineHero />
         <div className="relative bg-gray-100 py-14">
            <div className="mx-auto max-w-[1380px] px-4 md:px-8">
               <h1 className="text-2xl font-bold md:text-3xl">Magazine</h1>
               <p className="text-md mt-4 text-gray-600">
                  Discover our annual collections of featured creatives. Each
                  magazine showcases the exceptional talent and inspiring
                  stories of individuals who have made our lists throughout the
                  year.
               </p>
            </div>
         </div>
         <MagazineArchive />
      </main>
   );
}

import Image from "next/image";
import Link from "next/link";

export default function Footer() {
   const currentYear = new Date().getFullYear();

   return (
      <footer className="bg-black text-white" role="contentinfo">
         <div className="mx-auto max-w-[1380px] px-4 py-12 md:px-8">
            <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
               {/* Logo and Description */}
               <div className="lg:col-span-1">
                  <Link href="/" className="flex items-center">
                     <div className="relative mr-2 h-18 w-18">
                        <Image
                           src="/images/logo.png"
                           alt="Mlist Logo"
                           fill
                           className="object-contain"
                        />
                     </div>
                  </Link>
                  <p className="mt-4 text-sm text-white/70">
                     Honoring top creatives in design, music, film, tech,
                     fashion and more with exclusive lists, stories, features &
                     recognition.
                  </p>
               </div>

               {/* Navigation Links */}
               <div>
                  <h3 className="text-sm font-semibold tracking-wider uppercase">
                     Navigation
                  </h3>
                  <nav
                     className="mt-4 space-y-3"
                     aria-label="Footer navigation"
                  >
                     <div className="flex flex-col items-start space-y-3">
                        <Link
                           href="/"
                           className="text-sm text-white/70 transition-colors hover:text-white"
                        >
                           Home
                        </Link>
                        <Link
                           href="/the-list"
                           className="text-sm text-white/70 transition-colors hover:text-white"
                        >
                           The List
                        </Link>
                        <Link
                           href="/profiles"
                           className="text-sm text-white/70 transition-colors hover:text-white"
                        >
                           Profiles
                        </Link>
                        <Link
                           href="/gallery"
                           className="text-sm text-white/70 transition-colors hover:text-white"
                        >
                           Gallery
                        </Link>
                        <Link
                           href="/magazine"
                           className="text-sm text-white/70 transition-colors hover:text-white"
                        >
                           Magazine
                        </Link>
                        <Link
                           href="/newsletter"
                           className="text-sm text-white/70 transition-colors hover:text-white"
                        >
                           Newsletter
                        </Link>
                     </div>
                  </nav>
               </div>

               {/* Connect */}
               <div>
                  <h3 className="text-sm font-semibold tracking-wider uppercase">
                     Connect
                  </h3>
                  <div className="mt-4 space-y-3">
                     <a
                        href="mailto:<EMAIL>"
                        className="block text-sm text-white/70 transition-colors hover:text-white"
                        aria-label="Email <NAME_EMAIL>"
                     >
                        <EMAIL>
                     </a>
                     <div className="flex space-x-4">
                        <a
                           href="#"
                           className="text-white/70 transition-colors hover:text-white"
                           aria-label="Follow us on Instagram"
                        >
                           <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="24"
                              height="24"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              className="h-5 w-5"
                           >
                              <rect
                                 width="20"
                                 height="20"
                                 x="2"
                                 y="2"
                                 rx="5"
                                 ry="5"
                              />
                              <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z" />
                              <line x1="17.5" x2="17.51" y1="6.5" y2="6.5" />
                           </svg>
                        </a>
                        <a
                           href="#"
                           className="text-white/70 transition-colors hover:text-white"
                           aria-label="Follow us on Twitter"
                        >
                           <svg
                              className="h-5 w-5"
                              fill="currentColor"
                              viewBox="0 0 24 24"
                              aria-hidden="true"
                           >
                              <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                           </svg>
                        </a>
                        <a
                           href="#"
                           className="text-white/70 transition-colors hover:text-white"
                           aria-label="Follow us on LinkedIn"
                        >
                           <svg
                              className="h-5 w-5"
                              fill="currentColor"
                              viewBox="0 0 24 24"
                              aria-hidden="true"
                           >
                              <path
                                 fillRule="evenodd"
                                 d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"
                                 clipRule="evenodd"
                              />
                           </svg>
                        </a>
                     </div>
                  </div>
               </div>

               {/* Legal */}
               <div>
                  <h3 className="text-sm font-semibold tracking-wider uppercase">
                     Legal
                  </h3>
                  <div className="mt-4 space-y-3">
                     <Link
                        href="/privacy"
                        className="block text-sm text-white/70 transition-colors hover:text-white"
                     >
                        Privacy Policy
                     </Link>
                     <Link
                        href="/terms"
                        className="block text-sm text-white/70 transition-colors hover:text-white"
                     >
                        Terms of Service
                     </Link>
                     <Link
                        href="/cookies"
                        className="block text-sm text-white/70 transition-colors hover:text-white"
                     >
                        Cookie Policy
                     </Link>
                  </div>
               </div>
            </div>

            {/* Bottom Section */}
            <div className="mt-12 border-t border-white/10 pt-8">
               <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
                  <p className="text-sm text-white/70">
                     © {currentYear} Mlist. All rights reserved.
                  </p>
                  <div className="flex space-x-6">
                     <Link
                        href="/nominate"
                        className="text-sm text-white/70 transition-colors hover:text-white"
                     >
                        Nominate a Creative
                     </Link>
                     <Link
                        href="/contact"
                        className="text-sm text-white/70 transition-colors hover:text-white"
                     >
                        Contact Us
                     </Link>
                  </div>
               </div>
            </div>
         </div>
      </footer>
   );
}

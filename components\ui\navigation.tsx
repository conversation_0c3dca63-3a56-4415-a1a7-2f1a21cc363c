import ActiveLink from "@/components/ui/active-link";
import { But<PERSON> } from "@/components/ui/button";
import { Menu } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { Sheet, SheetContent, SheetTitle, SheetTrigger } from "./sheet";

export default function Navigation() {
   return (
      <header className="fixed top-0 right-0 left-0 z-55 h-[80px] bg-black px-2 text-white md:h-[100px]">
         <div className="mx-auto max-w-[1380px] px-4 md:px-8">
            <div className="flex h-[80px] items-center justify-between md:h-[100px]">
               <Link href="/" className="flex items-center">
                  <div className="relative mr-2 h-18 w-18">
                     <Image
                        src="/images/logo.png"
                        alt="Mlist Logo"
                        fill
                        className="object-contain"
                     />
                  </div>
               </Link>
               <nav className="hidden items-center space-x-8 md:flex">
                  <ActiveLink href="/" exactMatch>
                     Home
                  </ActiveLink>
                  <ActiveLink href="/the-list">The List</ActiveLink>
                  <ActiveLink href="/profiles">Profiles</ActiveLink>
                  <ActiveLink href="/gallery">Gallery</ActiveLink>
                  <ActiveLink href="/magazine">Magazine</ActiveLink>
                  <ActiveLink href="/newsletter">Newsletter</ActiveLink>
                  <Button
                     asChild
                     className="mx-0 rounded-2xl border-2 border-gray-400 bg-black px-7 py-5 text-base text-gray-300 shadow-md shadow-black hover:bg-gray-950 md:mx-4 lg:mx-6"
                     variant="secondary"
                  >
                     <Link
                        href="https://docs.google.com/forms/d/e/1FAIpQLSew1e_uhrpuLbtEVDM3gwjPzqiaDmW8KNQRgKy1BIJvCq97JA/viewform?usp=dialog"
                        target="_blank"
                     >
                        Nominate
                     </Link>
                  </Button>
               </nav>
               <Sheet>
                  <SheetTrigger asChild>
                     <Button variant="ghost" className="h-12 w-12 md:hidden">
                        <Menu className="size-7" />
                        <span className="sr-only">Toggle menu</span>
                     </Button>
                  </SheetTrigger>
                  <SheetContent side="top" className="z-90 w-full p-8">
                     <SheetTitle className="sr-only">Menu</SheetTitle>
                     <div className="flex h-full flex-col">
                        <nav className="my-6 flex flex-col divide-y divide-gray-200 border-y border-gray-200">
                           <Link className="py-4" href="/">
                              Home
                           </Link>
                           <Link className="py-4" href="/the-list">
                              The List
                           </Link>
                           <Link className="py-4" href="/profiles">
                              Profiles
                           </Link>
                           <Link className="py-4" href="/gallery">
                              Gallery
                           </Link>
                           <Link className="py-4" href="/magazine">
                              Magazine
                           </Link>
                           <Link className="py-4" href="/newsletter">
                              Newsletter
                           </Link>
                        </nav>
                        <div className="mt-auto">
                           <Button
                              asChild
                              className="mx-0 w-full rounded-2xl border-2 border-gray-400 bg-black px-7 py-5 text-base text-gray-300 shadow-md shadow-black hover:bg-gray-950 md:mx-4 lg:mx-6"
                              variant="secondary"
                           >
                              <Link
                                 href="https://docs.google.com/forms/d/e/1FAIpQLSew1e_uhrpuLbtEVDM3gwjPzqiaDmW8KNQRgKy1BIJvCq97JA/viewform?usp=dialog"
                                 target="_blank"
                              >
                                 Nominate
                              </Link>
                           </Button>
                        </div>
                     </div>
                  </SheetContent>
               </Sheet>
            </div>
         </div>
      </header>
   );
}

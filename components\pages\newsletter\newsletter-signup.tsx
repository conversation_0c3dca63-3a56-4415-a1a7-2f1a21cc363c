"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useState } from "react";

export default function NewsletterSignup() {
   const [email, setEmail] = useState("");
   const [isSubmitting, setIsSubmitting] = useState(false);
   const [submitStatus, setSubmitStatus] = useState<
      "idle" | "success" | "error"
   >("idle");
   const [errorMessage, setErrorMessage] = useState("");

   const validateEmail = (email: string) => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
   };

   const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();

      if (!email.trim()) {
         setErrorMessage("Email address is required");
         setSubmitStatus("error");
         return;
      }

      if (!validateEmail(email)) {
         setErrorMessage("Please enter a valid email address");
         setSubmitStatus("error");
         return;
      }

      setIsSubmitting(true);
      setErrorMessage("");

      try {
         // Simulate API call - replace with actual newsletter subscription logic
         await new Promise((resolve) => setTimeout(resolve, 1000));

         setSubmitStatus("success");
         setEmail("");
      } catch (error) {
         console.error("Error submitting newsletter signup:", error);
         setSubmitStatus("error");
         setErrorMessage("Something went wrong. Please try again.");
      } finally {
         setIsSubmitting(false);
      }
   };

   return (
      <section id="newsletter-signup" className="py-16 md:py-24 md:pt-12">
         <div className="mx-auto max-w-4xl px-4 md:px-8">
            <div className="text-center">
               <h2 className="text-2xl font-bold md:text-3xl lg:text-4xl">
                  Join Our Creative Community
               </h2>
               <p className="mt-6 text-base text-gray-600 md:text-lg">
                  Be the first to discover exceptional talent, read exclusive
                  interviews, and get insights from the creative industry&apos;s
                  most innovative minds.
               </p>
            </div>

            <div className="mt-12">
               <form onSubmit={handleSubmit} className="mx-auto max-w-md">
                  <div className="space-y-4">
                     <div>
                        <label htmlFor="newsletter-email" className="sr-only">
                           Email address
                        </label>
                        <Input
                           type="email"
                           id="newsletter-email"
                           value={email}
                           onChange={(e) => {
                              setEmail(e.target.value);
                              if (submitStatus === "error") {
                                 setSubmitStatus("idle");
                                 setErrorMessage("");
                              }
                           }}
                           placeholder="Enter your email address"
                           className="w-full rounded-lg border border-gray-300 px-4 py-3 text-lg focus:border-black focus:ring-2 focus:ring-black/20 focus:outline-none disabled:cursor-not-allowed disabled:bg-gray-100"
                           disabled={isSubmitting}
                           aria-describedby={
                              submitStatus === "error"
                                 ? "email-error"
                                 : undefined
                           }
                           aria-invalid={submitStatus === "error"}
                        />
                        {/* <input
                           type="email"
                           id="newsletter-email"
                           value={email}
                           onChange={(e) => {
                              setEmail(e.target.value);
                              if (submitStatus === "error") {
                                 setSubmitStatus("idle");
                                 setErrorMessage("");
                              }
                           }}
                           placeholder="Enter your email address"
                           className="w-full rounded-lg border border-gray-300 px-4 py-3 text-lg focus:border-black focus:ring-2 focus:ring-black/20 focus:outline-none disabled:cursor-not-allowed disabled:bg-gray-100"
                           disabled={isSubmitting}
                           aria-describedby={
                              submitStatus === "error"
                                 ? "email-error"
                                 : undefined
                           }
                           aria-invalid={submitStatus === "error"}
                        /> */}
                        {submitStatus === "error" && errorMessage && (
                           <p
                              id="email-error"
                              className="mt-2 text-sm text-red-600"
                              role="alert"
                           >
                              {errorMessage}
                           </p>
                        )}
                     </div>

                     <Button
                        type="submit"
                        disabled={isSubmitting}
                        className="w-full rounded-lg bg-black p-6 text-lg font-semibold text-white transition-all hover:bg-gray-800 disabled:cursor-not-allowed disabled:bg-gray-400"
                        size="lg"
                     >
                        {isSubmitting
                           ? "Subscribing..."
                           : "Subscribe to Newsletter"}
                     </Button>
                  </div>

                  {submitStatus === "success" && (
                     <div
                        className="mt-4 rounded-lg border border-green-200 bg-green-50 p-4 text-center"
                        role="alert"
                        aria-live="polite"
                     >
                        <p className="font-medium text-green-800">
                           🎉 Thank you for subscribing!
                        </p>
                        <p className="mt-1 text-sm text-green-700">
                           You&apos;ll receive our latest updates and exclusive
                           content.
                        </p>
                     </div>
                  )}
               </form>

               <div className="mt-8 text-center">
                  <p className="text-sm text-gray-500">
                     By subscribing, you agree to receive marketing emails from
                     Mlist. You can unsubscribe at any time.{" "}
                     <a
                        href="/privacy"
                        className="text-black underline hover:no-underline"
                     >
                        Privacy Policy
                     </a>
                  </p>
               </div>
            </div>

            {/* Newsletter Benefits */}
            <div className="mt-16 grid gap-8 md:grid-cols-3">
               <div className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-black text-white">
                     <svg
                        className="h-6 w-6"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                     >
                        <path
                           strokeLinecap="round"
                           strokeLinejoin="round"
                           strokeWidth={2}
                           d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253"
                        />
                     </svg>
                  </div>
                  <h3 className="text-lg font-semibold">Exclusive Content</h3>
                  <p className="mt-2 text-gray-600">
                     Get access to in-depth interviews and stories not available
                     anywhere else.
                  </p>
               </div>

               <div className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-black text-white">
                     <svg
                        className="h-6 w-6"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                     >
                        <path
                           strokeLinecap="round"
                           strokeLinejoin="round"
                           strokeWidth={2}
                           d="M13 10V3L4 14h7v7l9-11h-7z"
                        />
                     </svg>
                  </div>
                  <h3 className="text-lg font-semibold">Early Access</h3>
                  <p className="mt-2 text-gray-600">
                     Be the first to know about new featured creatives and
                     monthly list announcements.
                  </p>
               </div>

               <div className="text-center">
                  <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-black text-white">
                     <svg
                        className="h-6 w-6"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                        aria-hidden="true"
                     >
                        <path
                           strokeLinecap="round"
                           strokeLinejoin="round"
                           strokeWidth={2}
                           d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                        />
                     </svg>
                  </div>
                  <h3 className="text-lg font-semibold">Community Insights</h3>
                  <p className="mt-2 text-gray-600">
                     Discover trends and insights from the creative community
                     and industry leaders.
                  </p>
               </div>
            </div>
         </div>
      </section>
   );
}

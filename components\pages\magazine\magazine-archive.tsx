import MagazineItem from "./magazine-item";

export default function MagazineArchive() {
   const currentYear = new Date().getFullYear();

   const magazines = [
      {
         year: currentYear - 1, // 2023
         title: "Simply dummy text of the printing and typesetting industry",
         coverImage: "/images/hero/hero-bg.png",
         description:
            "Simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. ",
      },
      {
         year: currentYear - 2, // 2022
         title: "Simply dummy text of the printing and typesetting industry",
         coverImage: "/images/hero/hero-bg.png",
         description:
            "Simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. ",
      },
      {
         year: currentYear - 3, // 2021
         title: "Simply dummy text of the printing and typesetting industry",
         coverImage: "/images/hero/hero-bg.png",
         description:
            "Simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. ",
      },
   ];

   return (
      // <section className="mx-auto max-w-[1380px] px-4 py-16 md:px-8">
      <section className="mx-auto max-w-5xl px-4 py-16 md:px-8">
         {/* Magazine List - Vertical Layout */}
         <div className="space-y-0">
            {magazines.map((magazine) => (
               <MagazineItem
                  key={magazine.year}
                  year={magazine.year}
                  title={magazine.title}
                  coverImage={magazine.coverImage}
                  description={magazine.description}
               />
            ))}
         </div>

         {/* Newsletter CTA */}
         <div className="mt-16 text-center">
            <h3 className="mb-4 text-xl font-semibold">
               Stay Updated with Mlist
            </h3>
            <p className="mb-6 text-gray-600">
               Subscribe to our newsletter to receive the latest magazines and
               updates.
            </p>
            <a
               href="/newsletter"
               className="inline-block rounded-lg bg-black px-6 py-3 text-white transition-colors hover:bg-gray-800"
               aria-label="Subscribe to MLIST newsletter"
            >
               Join Our Newsletter
            </a>
         </div>
      </section>
   );
}

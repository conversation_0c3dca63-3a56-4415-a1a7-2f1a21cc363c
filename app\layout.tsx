import Footer from "@/components/ui/footer";
import Navigation from "@/components/ui/navigation";
import type { Metadata } from "next";
import localFont from "next/font/local";
import "./globals.css";

// Define the Avenir Next LT Pro font with multiple weights
const avenirNextLTPro = localFont({
   variable: "--font-avenir-next",
   display: "swap",
   src: [
      {
         path: "../public/fonts/Avenir-next-lt-pro/AvenirNextLTPro-Regular.otf",
         weight: "400",
         style: "normal",
      },
      {
         path: "../public/fonts/Avenir-next-lt-pro/AvenirNextLTPro-It.otf",
         weight: "400",
         style: "italic",
      },
      {
         path: "../public/fonts/Avenir-next-lt-pro/AvenirNextLTPro-Demi.otf",
         weight: "600",
         style: "normal",
      },
      {
         path: "../public/fonts/Avenir-next-lt-pro/AvenirNextLTPro-DemiIt.otf",
         weight: "600",
         style: "italic",
      },
      {
         path: "../public/fonts/Avenir-next-lt-pro/AvenirNextLTPro-Bold.otf",
         weight: "700",
         style: "normal",
      },
   ],
});

export const metadata: Metadata = {
   title: "Mlist",
   description:
      "Mlist honors top creatives in design, music, film, tech, fashion and more with exclusive lists, stories, features & recognition for those who made the list.",
};

export default function RootLayout({
   children,
}: Readonly<{
   children: React.ReactNode;
}>) {
   return (
      <html lang="en">
         <body className={`${avenirNextLTPro.variable} antialiased`}>
            <Navigation />
            {children}
            <Footer />
         </body>
      </html>
   );
}

import { Button } from "@/components/ui/button";
import Image from "next/image";
import Link from "next/link";

export default function MagazineHero() {
   const currentYear = new Date().getFullYear();
   const previousYear = currentYear - 1;

   return (
      <section className="relative h-screen overflow-hidden">
         {/* Background Image */}
         <div className="absolute inset-0 z-0">
            <Image
               src="/images/hero/hero-bg.png"
               alt="Magazine background"
               fill
               priority
               className="object-cover"
            />
         </div>

         {/* Hero Content */}
         <div className="relative z-10 mx-auto flex h-full max-w-[1380px] items-center justify-start px-4 md:px-8">
            <div className="max-w-2xl">
               {/* Main Hero Text */}
               <h1 className="text-5xl font-bold text-white md:text-6xl lg:text-7xl">
                  {previousYear}
               </h1>
               <h2 className="mt-4 text-3xl font-bold text-white md:text-4xl">
                  Annual Collection
               </h2>
               <p className="text-md mt-6 text-white/90 md:text-lg lg:text-xl">
                  Featuring the year&apos;s most exceptional creatives who
                  redefined their industries and inspired a new generation of
                  talent.
               </p>

               {/* Download Button */}
               <div className="mt-8">
                  <Button
                     asChild
                     className="rounded-2xl border-2 border-gray-400 bg-black px-8 py-6 text-lg text-gray-200 shadow-md shadow-black hover:bg-gray-950"
                     variant="secondary"
                     aria-label={`Download Mlist Magazine ${previousYear}`}
                  >
                     <Link href="#">Download {previousYear} Edition</Link>
                  </Button>
               </div>
            </div>
         </div>
      </section>
   );
}
